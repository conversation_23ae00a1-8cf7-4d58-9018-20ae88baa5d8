#!/usr/bin/env python3
"""
Simple MCP Server Test Script

Usage: python server_test.py /path/to/server.py
"""
import asyncio
import sys
from typing import Optional
from contextlib import AsyncExitStack

from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

from dotenv import load_dotenv

load_dotenv()  # load environment variables from .env


class MCPServerTester:
    def __init__(self):
        self.session: Optional[ClientSession] = None
        self.exit_stack = AsyncExitStack()

    async def connect_to_server(self, server_script_path: str):
        """Connect to an MCP server"""
        is_python = server_script_path.endswith('.py')
        is_js = server_script_path.endswith('.js')
        if not (is_python or is_js):
            raise ValueError("Server script must be a .py or .js file")

        command = "python" if is_python else "node"
        server_params = StdioServerParameters(
            command=command,
            args=[server_script_path],
            env=None
        )

        print(f"Connecting to server: {server_script_path}")
        stdio_transport = await self.exit_stack.enter_async_context(stdio_client(server_params))
        self.stdio, self.write = stdio_transport
        self.session = await self.exit_stack.enter_async_context(ClientSession(self.stdio, self.write))

        await self.session.initialize()
        print("✓ Connected successfully!")

    async def list_tools(self):
        """List available tools from the server"""
        response = await self.session.list_tools()
        tools = response.tools
        
        print(f"\nAvailable tools ({len(tools)}):")
        for tool in tools:
            print(f"  • {tool.name}: {tool.description}")
            if tool.inputSchema:
                print(f"    Input schema: {tool.inputSchema}")
        
        return tools

    async def test_tool_call(self, tool_name: str, args: dict = None):
        """Test calling a specific tool"""
        if args is None:
            args = {}
        
        print(f"\nTesting tool: {tool_name}")
        print(f"Arguments: {args}")
        
        try:
            result = await self.session.call_tool(tool_name, args)
            print(f"✓ Tool call successful!")
            print(f"Result: {result.content}")
            return result
        except Exception as e:
            print(f"✗ Tool call failed: {str(e)}")
            return None

    async def cleanup(self):
        """Clean up resources"""
        await self.exit_stack.aclose()

    async def run_basic_test(self):
        """Run a basic test of the server"""
        try:
            # List available tools
            tools = await self.list_tools()
            
            # If there are tools, we could test calling one
            if tools:
                print(f"\n✓ Server is working! Found {len(tools)} tool(s)")
            else:
                print("\n⚠ Server connected but no tools found")
                
        except Exception as e:
            print(f"\n✗ Error during testing: {str(e)}")
            raise


async def main():
    if len(sys.argv) != 2:
        print("Usage: python server_test.py /path/to/server.py")
        sys.exit(1)
    
    server_script = sys.argv[1]
    
    tester = MCPServerTester()
    try:
        await tester.connect_to_server(server_script)
        await tester.run_basic_test()
        
        # Keep the connection open for a moment to ensure everything is stable
        print("\nConnection test completed successfully!")
        
    except Exception as e:
        print(f"Test failed: {str(e)}")
        sys.exit(1)
    finally:
        await tester.cleanup()


if __name__ == "__main__":
    asyncio.run(main())
