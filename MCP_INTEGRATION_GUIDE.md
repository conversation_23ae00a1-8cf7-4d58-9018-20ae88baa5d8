# MCP Integration Guide

The workflow engine now supports executing tools from MCP (Model Context Protocol) servers alongside local tools, providing a unified execution environment for both local and remote tool capabilities.

## Overview

The MCP integration allows workflows to:
- Execute tools from multiple MCP servers
- Mix local tools and MCP tools in the same workflow
- Automatically route tool calls to the appropriate server
- Handle MCP server connections and lifecycle management
- Provide unified error handling and reporting

## Architecture

### Components

1. **MCPAdapter** - Manages connections to multiple MCP servers
2. **MCPServerConnection** - Handles individual server connections
3. **WorkflowEngine** - Enhanced to support both local and MCP tools
4. **MCPServerConfig** - Configuration model for MCP servers

### Tool Execution Flow

```
Workflow Step → WorkflowEngine → Tool Router → Local Tool | MCP Tool
                                                     ↓           ↓
                                              tools.execute  MCPAdapter
```

## Configuration

### Workflow YAML Configuration

Add MCP servers to your workflow configuration:

```yaml
name: "MCP Integration Demo"
description: "Workflow using both local and MCP tools"

# MCP Server configurations
mcp_servers:
  - name: "github_server"
    script_path: "./mcp_servers/github_server.py"
    auto_connect: true
    env:
      GITHUB_TOKEN: "${GITHUB_TOKEN}"
  
  - name: "file_server"
    script_path: "./mcp_servers/file_server.js"
    auto_connect: true

steps:
  # Local tool
  - name: "create_directory"
    tool_name: "terminal.execute"
    input_data:
      command: "mkdir -p ./output"
  
  # MCP tool
  - name: "github_operation"
    tool_name: "github_tool.get_repo_info"
    input_data:
      repo_url: "https://github.com/user/repo"
    depends_on: ["create_directory"]
```

### MCP Server Configuration Options

- **name**: Unique identifier for the server
- **script_path**: Path to the MCP server script (.py or .js)
- **auto_connect**: Whether to automatically connect (default: true)
- **env**: Environment variables for the server process

## Usage Examples

### Basic MCP Tool Execution

```python
from workflow.engine import WorkflowEngine
from workflow.models import WorkflowConfig, WorkflowStep, MCPServerConfig

config = WorkflowConfig(
    name="MCP Example",
    steps=[
        WorkflowStep(
            name="mcp_step",
            tool_name="mcp_tool.some_operation",
            input_data={"param": "value"}
        )
    ],
    mcp_servers=[
        MCPServerConfig(
            name="my_server",
            script_path="./my_mcp_server.py"
        )
    ]
)

engine = WorkflowEngine()
result = await engine.execute(config)
```

### Mixed Local and MCP Tools

```yaml
steps:
  # Local file operation
  - name: "create_file"
    tool_name: "file.write"
    input_data:
      path: "./data.txt"
      content: "Hello World"
  
  # MCP tool operation
  - name: "process_file"
    tool_name: "data_processor.analyze"
    input_data:
      file_path: "./data.txt"
    depends_on: ["create_file"]
  
  # Local cleanup
  - name: "cleanup"
    tool_name: "file.delete"
    input_data:
      path: "./data.txt"
    depends_on: ["process_file"]
```

## Features

### Automatic Tool Routing

The workflow engine automatically determines whether a tool is:
- A local tool (executed via the tools module)
- An MCP tool (executed via MCP adapter)

No special configuration needed - just use the tool name.

### Connection Management

- **Automatic Connection**: Servers with `auto_connect: true` connect at workflow start
- **Lifecycle Management**: Connections are properly cleaned up after workflow execution
- **Error Handling**: Connection failures are logged but don't stop the workflow

### Parallel Execution

MCP tools support the same parallel execution capabilities as local tools:

```yaml
steps:
  - name: "parallel_mcp_1"
    tool_name: "server1.tool"
    input_data: {...}
    depends_on: ["setup"]
  
  - name: "parallel_mcp_2"
    tool_name: "server2.tool"
    input_data: {...}
    depends_on: ["setup"]
```

### Dry Run Support

Dry runs show MCP server configuration and planned tool execution:

```bash
🔌 MCP Servers (2):
   • github_server: ./mcp_servers/github_server.py (auto-connect)
   • file_server: ./mcp_servers/file_server.js (manual)

📊 Execution plan (3 levels):
   Level 1: setup (sequential)
      • setup: terminal.execute
   Level 2: mcp_task1, mcp_task2 (parallel)
      • mcp_task1: github_tool.clone
      • mcp_task2: file_server.process
```

## Error Handling

### MCP Server Connection Errors

- Non-critical: Workflow continues if MCP server connection fails
- Tools from failed servers will return error results
- Error messages include server name and failure reason

### Tool Execution Errors

MCP tool errors are handled the same as local tool errors:
- Returned as `ToolOutput` with error status
- Workflow continues unless `fail_fast` is enabled
- Error details included in workflow results

## Best Practices

### Server Organization

1. **Group Related Tools**: Put related tools on the same MCP server
2. **Environment Isolation**: Use separate servers for different environments
3. **Resource Management**: Consider server startup time and resource usage

### Tool Naming

1. **Namespace Convention**: Use clear namespaces (e.g., `github_tool.clone`)
2. **Avoid Conflicts**: Ensure MCP tool names don't conflict with local tools
3. **Descriptive Names**: Use descriptive names for better workflow readability

### Configuration Management

1. **Environment Variables**: Use env vars for sensitive data (API keys, tokens)
2. **Path Management**: Use relative paths for portability
3. **Auto-connect Strategy**: Only auto-connect to essential servers

## Troubleshooting

### Common Issues

1. **Server Script Not Found**
   ```
   Error: MCP server script must be a .py or .js file
   ```
   - Check script path and file extension

2. **Connection Timeout**
   ```
   Failed to connect to MCP server: Connection timeout
   ```
   - Verify server script is executable
   - Check environment variables and dependencies

3. **Tool Not Found**
   ```
   MCP tool 'tool_name' not found on any connected server
   ```
   - Verify tool name spelling
   - Check if server connected successfully
   - Use dry run to see available tools

### Debug Information

Enable verbose logging to see MCP connection details:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Integration with Existing Workflows

Existing workflows continue to work unchanged. To add MCP support:

1. Add `mcp_servers` section to workflow YAML
2. Replace tool names with MCP tool names where desired
3. Test with dry run to verify configuration

The workflow engine maintains full backward compatibility with existing local-only workflows.
