"""
Copier Tools

Template cloning and project generation tools using Copier.

Author: <PERSON>
"""

import asyncio
from pathlib import Path
from typing import Dict, Any, List, Optional, Union
from pydantic import BaseModel, Field, validator

from .base import BaseTool, ToolOutput, ToolStatus, register_tool


class CopierCloneInput(BaseModel):
    """Input model for copier clone operation"""
    template_url: str = Field(..., description="GitHub repository URL or local template path")
    destination: str = Field(..., description="Destination directory for the cloned template")
    data: Optional[Dict[str, Any]] = Field(None, description="Template variables/answers as key-value pairs")
    overwrite: bool = Field(False, description="Overwrite destination if it exists")
    skip_if_exists: bool = Field(False, description="Skip cloning if destination already exists")
    checkout: Optional[str] = Field(None, description="Git reference (branch, tag, commit) to checkout")
    
    class Config:
        json_schema_extra = {
            "examples": [
                {
                    "template_url": "https://github.com/copier-org/copier.git",
                    "destination": "./my-project",
                    "data": {"project_name": "My Project", "author": "<PERSON>e"}
                },
                {
                    "template_url": "gh:user/template-repo",
                    "destination": "./output",
                    "overwrite": True,
                    "checkout": "v1.0.0"
                }
            ]
        }

    @validator('template_url')
    def validate_template_url(cls, v):
        """Validate template URL format"""
        if not v:
            raise ValueError("Template URL cannot be empty")
        
        # Allow various formats: full URLs, GitHub shortcuts, local paths
        if v.startswith(('http://', 'https://', 'git://', 'ssh://', 'gh:', 'gl:')):
            return v
        elif Path(v).exists():
            return v
        else:
            # Assume it's a GitHub shorthand like "user/repo"
            if '/' in v and not v.startswith('.'):
                return v
            raise ValueError(f"Invalid template URL or path: {v}")

    @validator('destination')
    def validate_destination(cls, v):
        """Validate destination path"""
        if not v:
            raise ValueError("Destination cannot be empty")
        return v


class CopierCloneResult(BaseModel):
    """Result from copier clone operation"""
    template_url: str = Field(..., description="Template URL that was cloned")
    destination: str = Field(..., description="Absolute path to the destination directory")
    files_created: List[str] = Field(..., description="List of files created during cloning")
    template_data: Dict[str, Any] = Field(..., description="Template variables used")
    success: bool = Field(..., description="Whether the operation was successful")
    message: str = Field(..., description="Success or error message")
    
    class Config:
        arbitrary_types_allowed = True


class CopierCloneTool(BaseTool):
    """Tool for cloning GitHub templates using Copier"""

    def __init__(self):
        super().__init__(namespace="copier", name="clone")

    def get_description(self) -> str:
        return "Clone a GitHub template repository using Copier with template variable substitution"

    def get_input_model(self):
        return CopierCloneInput
    
    async def execute(self, input_data: Dict[str, Any]) -> ToolOutput:
        start_time = asyncio.get_event_loop().time()

        # Parse input using Pydantic model
        parsed_input = self.parse_input(input_data)

        template_url = parsed_input.template_url
        destination = parsed_input.destination
        data = parsed_input.data or {}
        overwrite = parsed_input.overwrite
        skip_if_exists = parsed_input.skip_if_exists
        checkout = parsed_input.checkout
        
        try:
            # Convert destination to absolute path
            dest_path = Path(destination).resolve()
            
            # Check if destination exists
            if dest_path.exists():
                if skip_if_exists:
                    result = CopierCloneResult(
                        template_url=template_url,
                        destination=str(dest_path),
                        files_created=[],
                        template_data=data,
                        success=True,
                        message=f"Destination already exists, skipped: {dest_path}"
                    )
                    return ToolOutput(
                        status=ToolStatus.SUCCESS,
                        data=result,
                        execution_time=round(asyncio.get_event_loop().time() - start_time, 3)
                    )
                elif not overwrite:
                    return ToolOutput(
                        status=ToolStatus.ERROR,
                        error=f"Destination already exists and overwrite=False: {dest_path}",
                        execution_time=round(asyncio.get_event_loop().time() - start_time, 3)
                    )
            
            # Import copier here to avoid import errors if not installed
            try:
                import copier
            except ImportError:
                return ToolOutput(
                    status=ToolStatus.ERROR,
                    error="Copier is not installed. Please install it with: pip install copier",
                    execution_time=round(asyncio.get_event_loop().time() - start_time, 3)
                )
            
            # Prepare copier arguments
            copier_kwargs = {
                'src_path': template_url,
                'dst_path': str(dest_path),
                'data': data,
                'overwrite': overwrite,
            }

            # Add optional parameters that are supported
            if checkout:
                copier_kwargs['vcs_ref'] = checkout
            # Note: subdirectory is not supported in run_copy, would need to be part of src_path
            
            # Track files before cloning
            files_before = set()
            if dest_path.exists():
                files_before = {str(p.relative_to(dest_path)) for p in dest_path.rglob('*') if p.is_file()}
            
            # Execute copier
            try:
                # Use copier.run_copy for the main operation
                copier.run_copy(**copier_kwargs)
                
                # Track files after cloning
                files_after = set()
                if dest_path.exists():
                    files_after = {str(p.relative_to(dest_path)) for p in dest_path.rglob('*') if p.is_file()}
                
                files_created = list(files_after - files_before)
                
                result = CopierCloneResult(
                    template_url=template_url,
                    destination=str(dest_path),
                    files_created=sorted(files_created),
                    template_data=data,
                    success=True,
                    message=f"Successfully cloned template to {dest_path}"
                )
                
                return ToolOutput(
                    status=ToolStatus.SUCCESS,
                    data=result,
                    execution_time=round(asyncio.get_event_loop().time() - start_time, 3)
                )
                
            except Exception as copier_error:
                # Handle copier-specific errors
                error_msg = f"Copier execution failed: {str(copier_error)}"
                
                result = CopierCloneResult(
                    template_url=template_url,
                    destination=str(dest_path),
                    files_created=[],
                    template_data=data,
                    success=False,
                    message=error_msg
                )
                
                return ToolOutput(
                    status=ToolStatus.ERROR,
                    data=result,
                    error=error_msg,
                    execution_time=round(asyncio.get_event_loop().time() - start_time, 3)
                )
                
        except Exception as e:
            execution_time = round(asyncio.get_event_loop().time() - start_time, 3)
            
            result = CopierCloneResult(
                template_url=template_url,
                destination=destination,
                files_created=[],
                template_data=data,
                success=False,
                message=f"Execution error: {str(e)}"
            )
            
            return ToolOutput(
                status=ToolStatus.ERROR,
                data=result,
                error=str(e),
                execution_time=execution_time
            )


class CopierUpdateInput(BaseModel):
    """Input model for copier update operation"""
    project_path: str = Field(..., description="Path to the existing project to update")
    data: Optional[Dict[str, Any]] = Field(None, description="Updated template variables/answers as key-value pairs")
    checkout: Optional[str] = Field(None, description="Git reference (branch, tag, commit) to update to")

    class Config:
        json_schema_extra = {
            "examples": [
                {
                    "project_path": "./my-project",
                    "data": {"version": "2.0.0", "author": "Updated Author"}
                },
                {
                    "project_path": "./output",
                    "checkout": "v2.0.0"
                }
            ]
        }

    @validator('project_path')
    def validate_project_path(cls, v):
        """Validate project path exists"""
        if not v:
            raise ValueError("Project path cannot be empty")

        path = Path(v)
        if not path.exists():
            raise ValueError(f"Project path does not exist: {v}")

        # Check if it's a copier project (has .copier-answers.yml)
        answers_file = path / ".copier-answers.yml"
        if not answers_file.exists():
            raise ValueError(f"Not a Copier project (missing .copier-answers.yml): {v}")

        return v


class CopierUpdateResult(BaseModel):
    """Result from copier update operation"""
    project_path: str = Field(..., description="Absolute path to the updated project")
    template_url: str = Field(..., description="Template URL that was used for update")
    files_modified: List[str] = Field(..., description="List of files modified during update")
    template_data: Dict[str, Any] = Field(..., description="Template variables used")
    success: bool = Field(..., description="Whether the operation was successful")
    message: str = Field(..., description="Success or error message")

    class Config:
        arbitrary_types_allowed = True


class CopierUpdateTool(BaseTool):
    """Tool for updating an existing Copier project"""

    def __init__(self):
        super().__init__(namespace="copier", name="update")

    def get_description(self) -> str:
        return "Update an existing Copier project to the latest template version"

    def get_input_model(self):
        return CopierUpdateInput

    async def execute(self, input_data: Dict[str, Any]) -> ToolOutput:
        start_time = asyncio.get_event_loop().time()

        # Parse input using Pydantic model
        parsed_input = self.parse_input(input_data)

        project_path = parsed_input.project_path
        data = parsed_input.data or {}
        checkout = parsed_input.checkout

        try:
            # Convert project path to absolute path
            proj_path = Path(project_path).resolve()

            # Import copier here to avoid import errors if not installed
            try:
                import copier
            except ImportError:
                return ToolOutput(
                    status=ToolStatus.ERROR,
                    error="Copier is not installed. Please install it with: pip install copier",
                    execution_time=round(asyncio.get_event_loop().time() - start_time, 3)
                )

            # Read current answers to get template URL
            answers_file = proj_path / ".copier-answers.yml"
            try:
                import yaml
                with open(answers_file, 'r') as f:
                    current_answers = yaml.safe_load(f) or {}
                template_url = current_answers.get('_src_path', 'Unknown')
            except Exception:
                template_url = 'Unknown'

            # Prepare copier arguments
            copier_kwargs = {
                'dst_path': str(proj_path),
                'data': data,
            }

            # Add optional parameters
            if checkout:
                copier_kwargs['vcs_ref'] = checkout

            # Track files before update
            files_before = {}
            if proj_path.exists():
                for p in proj_path.rglob('*'):
                    if p.is_file():
                        try:
                            files_before[str(p.relative_to(proj_path))] = p.stat().st_mtime
                        except (OSError, ValueError):
                            continue

            # Execute copier update
            try:
                # Use copier.run_update for updating
                copier.run_update(**copier_kwargs)

                # Track files after update
                files_after = {}
                files_modified = []
                if proj_path.exists():
                    for p in proj_path.rglob('*'):
                        if p.is_file():
                            try:
                                rel_path = str(p.relative_to(proj_path))
                                new_mtime = p.stat().st_mtime
                                files_after[rel_path] = new_mtime

                                # Check if file was modified
                                if rel_path in files_before:
                                    if abs(new_mtime - files_before[rel_path]) > 1:  # 1 second tolerance
                                        files_modified.append(rel_path)
                                else:
                                    # New file
                                    files_modified.append(rel_path)
                            except (OSError, ValueError):
                                continue

                result = CopierUpdateResult(
                    project_path=str(proj_path),
                    template_url=template_url,
                    files_modified=sorted(files_modified),
                    template_data=data,
                    success=True,
                    message=f"Successfully updated project at {proj_path}"
                )

                return ToolOutput(
                    status=ToolStatus.SUCCESS,
                    data=result,
                    execution_time=round(asyncio.get_event_loop().time() - start_time, 3)
                )

            except Exception as copier_error:
                # Handle copier-specific errors
                error_msg = f"Copier update failed: {str(copier_error)}"

                result = CopierUpdateResult(
                    project_path=str(proj_path),
                    template_url=template_url,
                    files_modified=[],
                    template_data=data,
                    success=False,
                    message=error_msg
                )

                return ToolOutput(
                    status=ToolStatus.ERROR,
                    data=result,
                    error=error_msg,
                    execution_time=round(asyncio.get_event_loop().time() - start_time, 3)
                )

        except Exception as e:
            execution_time = round(asyncio.get_event_loop().time() - start_time, 3)

            result = CopierUpdateResult(
                project_path=project_path,
                template_url="Unknown",
                files_modified=[],
                template_data=data,
                success=False,
                message=f"Execution error: {str(e)}"
            )

            return ToolOutput(
                status=ToolStatus.ERROR,
                data=result,
                error=str(e),
                execution_time=execution_time
            )


# Register the copier tools
register_tool(CopierCloneTool())
register_tool(CopierUpdateTool())
