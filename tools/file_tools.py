"""
File Tools

File system operation tools using the base tool system.

Author: <PERSON>
"""

import os
import shutil
from pathlib import Path
from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field, validator

from .base import BaseTool, ToolOutput, ToolStatus, register_tool


class FileReadInput(BaseModel):
    """Input model for file read operation"""
    path: str = Field(..., description="Path to the file to read")
    encoding: str = Field("utf-8", description="File encoding")
    max_size: int = Field(1048576, ge=1, description="Maximum file size to read in bytes (default: 1MB)")

    class Config:
        json_schema_extra = {
            "examples": [
                {"path": "example.txt"},
                {"path": "data.csv", "encoding": "utf-8", "max_size": 5242880}
            ]
        }


class FileWriteInput(BaseModel):
    """Input model for file write operation"""
    path: str = Field(..., description="Path to the file to write")
    content: str = Field(..., description="Content to write to the file")
    encoding: str = Field("utf-8", description="File encoding")
    create_dirs: bool = Field(False, description="Create parent directories if they don't exist")
    overwrite: bool = Field(False, description="Overwrite file if it exists")

    class Config:
        json_schema_extra = {
            "examples": [
                {"path": "output.txt", "content": "Hello World!", "overwrite": True},
                {"path": "data/output.json", "content": "{}", "create_dirs": True}
            ]
        }


class FileListInput(BaseModel):
    """Input model for file list operation"""
    path: str = Field(".", description="Path to the directory to list")
    recursive: bool = Field(False, description="List recursively")
    include_hidden: bool = Field(False, description="Include hidden files")
    pattern: Optional[str] = Field(None, description="Glob pattern to filter files")

    class Config:
        json_schema_extra = {
            "examples": [
                {"path": ".", "pattern": "*.py"},
                {"path": "/home/<USER>", "recursive": True, "include_hidden": False}
            ]
        }


class FileDeleteInput(BaseModel):
    """Input model for file delete operation"""
    path: str = Field(..., description="Path to the file or directory to delete")
    recursive: bool = Field(False, description="Delete directories recursively")
    force: bool = Field(False, description="Force deletion without confirmation")

    class Config:
        json_schema_extra = {
            "examples": [
                {"path": "temp.txt"},
                {"path": "temp_dir", "recursive": True, "force": True}
            ]
        }


class FileInfo(BaseModel):
    """Information about a file or directory"""
    name: str = Field(..., description="File or directory name")
    path: str = Field(..., description="Absolute path to the file")
    relative_path: str = Field(..., description="Relative path from the base directory")
    type: str = Field(..., description="Type: 'file' or 'directory'")
    size: Optional[int] = Field(None, description="File size in bytes (None for directories)")
    modified: float = Field(..., description="Last modified timestamp")

    class Config:
        arbitrary_types_allowed = True


class FileReadResult(BaseModel):
    """Result from reading a file"""
    content: str = Field(..., description="File content")
    size: int = Field(..., ge=0, description="File size in bytes")
    encoding: str = Field(..., description="File encoding used")
    path: str = Field(..., description="Absolute path to the file")

    class Config:
        arbitrary_types_allowed = True


class FileWriteResult(BaseModel):
    """Result from writing a file"""
    path: str = Field(..., description="Absolute path to the written file")
    size: int = Field(..., ge=0, description="Number of bytes written")
    encoding: str = Field(..., description="Encoding used for writing")
    created: bool = Field(..., description="Whether the file was newly created")

    class Config:
        arbitrary_types_allowed = True


class FileListResult(BaseModel):
    """Result from listing directory contents"""
    directory: str = Field(..., description="Absolute path to the directory")
    file_count: int = Field(..., ge=0, description="Number of files found")
    directory_count: int = Field(..., ge=0, description="Number of directories found")
    total_count: int = Field(..., ge=0, description="Total number of items found")
    files: List[FileInfo] = Field(..., description="List of files and directories")

    class Config:
        arbitrary_types_allowed = True

    @validator('total_count')
    def validate_total_count(cls, v, values):
        """Validate that total_count equals file_count + directory_count"""
        if 'file_count' in values and 'directory_count' in values:
            expected = values['file_count'] + values['directory_count']
            if v != expected:
                raise ValueError(f"total_count ({v}) must equal file_count + directory_count ({expected})")
        return v


class FileDeleteResult(BaseModel):
    """Result from deleting a file or directory"""
    deleted_path: str = Field(..., description="Absolute path of the deleted item")
    type: str = Field(..., description="Type of deleted item: 'file' or 'directory'")
    recursive: bool = Field(..., description="Whether deletion was recursive")

    class Config:
        arbitrary_types_allowed = True


class FileReadTool(BaseTool):
    """Tool for reading file contents"""

    def __init__(self):
        super().__init__(namespace="file", name="read")

    def get_description(self) -> str:
        return "Read the contents of a file"

    def get_input_model(self):
        return FileReadInput
    
    async def execute(self, input_data: Dict[str, Any]) -> ToolOutput:
        # Parse input using Pydantic model
        parsed_input = self.parse_input(input_data)

        file_path = parsed_input.path
        encoding = parsed_input.encoding
        max_size = parsed_input.max_size
        
        try:
            path = Path(file_path)
            
            if not path.exists():
                return ToolOutput(
                    status=ToolStatus.ERROR,
                    error=f"File does not exist: {file_path}"
                )
            
            if not path.is_file():
                return ToolOutput(
                    status=ToolStatus.ERROR,
                    error=f"Path is not a file: {file_path}"
                )
            
            file_size = path.stat().st_size
            if file_size > max_size:
                return ToolOutput(
                    status=ToolStatus.ERROR,
                    error=f"File too large: {file_size} bytes (max: {max_size})"
                )
            
            content = path.read_text(encoding=encoding)
            
            result = FileReadResult(
                content=content,
                size=file_size,
                encoding=encoding,
                path=str(path.absolute())
            )

            return ToolOutput(
                status=ToolStatus.SUCCESS,
                data=result
            )
            
        except Exception as e:
            return ToolOutput(
                status=ToolStatus.ERROR,
                error=f"Failed to read file: {str(e)}"
            )


class FileWriteTool(BaseTool):
    """Tool for writing content to files"""

    def __init__(self):
        super().__init__(namespace="file", name="write")

    def get_description(self) -> str:
        return "Write content to a file"

    def get_input_model(self):
        return FileWriteInput
    
    async def execute(self, input_data: Dict[str, Any]) -> ToolOutput:
        # Parse input using Pydantic model
        parsed_input = self.parse_input(input_data)

        file_path = parsed_input.path
        content = parsed_input.content
        encoding = parsed_input.encoding
        create_dirs = parsed_input.create_dirs
        overwrite = parsed_input.overwrite
        
        try:
            path = Path(file_path)
            
            # Check if file exists and overwrite is not allowed
            if path.exists() and not overwrite:
                return ToolOutput(
                    status=ToolStatus.ERROR,
                    error=f"File already exists and overwrite is disabled: {file_path}"
                )
            
            # Create parent directories if requested
            if create_dirs:
                path.parent.mkdir(parents=True, exist_ok=True)
            elif not path.parent.exists():
                return ToolOutput(
                    status=ToolStatus.ERROR,
                    error=f"Parent directory does not exist: {path.parent}"
                )
            
            # Check if file existed before writing
            file_existed = path.exists()

            # Write the content
            path.write_text(content, encoding=encoding)

            result = FileWriteResult(
                path=str(path.absolute()),
                size=len(content.encode(encoding)),
                encoding=encoding,
                created=not file_existed
            )

            return ToolOutput(
                status=ToolStatus.SUCCESS,
                data=result
            )
            
        except Exception as e:
            return ToolOutput(
                status=ToolStatus.ERROR,
                error=f"Failed to write file: {str(e)}"
            )


class FileListTool(BaseTool):
    """Tool for listing directory contents"""
    
    def __init__(self):
        super().__init__(namespace="file", name="list")
    
    def get_description(self) -> str:
        return "List contents of a directory"

    def get_input_model(self):
        return FileListInput
    
    async def execute(self, input_data: Dict[str, Any]) -> ToolOutput:
        # Parse input using Pydantic model
        parsed_input = self.parse_input(input_data)

        dir_path = parsed_input.path
        recursive = parsed_input.recursive
        include_hidden = parsed_input.include_hidden
        pattern = parsed_input.pattern
        
        try:
            path = Path(dir_path)
            
            if not path.exists():
                return ToolOutput(
                    status=ToolStatus.ERROR,
                    error=f"Directory does not exist: {dir_path}"
                )
            
            if not path.is_dir():
                return ToolOutput(
                    status=ToolStatus.ERROR,
                    error=f"Path is not a directory: {dir_path}"
                )
            
            # Get file list
            if recursive:
                if pattern:
                    files = list(path.rglob(pattern))
                else:
                    files = list(path.rglob("*"))
            else:
                if pattern:
                    files = list(path.glob(pattern))
                else:
                    files = list(path.iterdir())
            
            # Filter hidden files if requested
            if not include_hidden:
                files = [f for f in files if not f.name.startswith('.')]
            
            # Build file info
            file_list = []
            for file_path in sorted(files):
                try:
                    stat = file_path.stat()
                    file_info = FileInfo(
                        name=file_path.name,
                        path=str(file_path.absolute()),
                        relative_path=str(file_path.relative_to(path)),
                        type="directory" if file_path.is_dir() else "file",
                        size=stat.st_size if file_path.is_file() else None,
                        modified=stat.st_mtime
                    )
                    file_list.append(file_info)
                except (OSError, PermissionError):
                    # Skip files we can't access
                    continue

            file_count = len([f for f in file_list if f.type == "file"])
            directory_count = len([f for f in file_list if f.type == "directory"])

            result = FileListResult(
                directory=str(path.absolute()),
                file_count=file_count,
                directory_count=directory_count,
                total_count=len(file_list),
                files=file_list
            )

            return ToolOutput(
                status=ToolStatus.SUCCESS,
                data=result
            )
            
        except Exception as e:
            return ToolOutput(
                status=ToolStatus.ERROR,
                error=f"Failed to list directory: {str(e)}"
            )


class FileDeleteTool(BaseTool):
    """Tool for deleting files and directories"""
    
    def __init__(self):
        super().__init__(namespace="file", name="delete")
    
    def get_description(self) -> str:
        return "Delete a file or directory"

    def get_input_model(self):
        return FileDeleteInput
    
    async def execute(self, input_data: Dict[str, Any]) -> ToolOutput:
        # Parse input using Pydantic model
        parsed_input = self.parse_input(input_data)

        file_path = parsed_input.path
        recursive = parsed_input.recursive
        force = parsed_input.force
        
        try:
            path = Path(file_path)
            
            if not path.exists():
                return ToolOutput(
                    status=ToolStatus.ERROR,
                    error=f"Path does not exist: {file_path}"
                )
            
            # Safety check - don't delete important system paths
            abs_path = path.absolute()
            dangerous_paths = [Path.home(), Path("/"), Path("C:\\") if os.name == 'nt' else None]
            dangerous_paths = [p for p in dangerous_paths if p is not None]
            
            if abs_path in dangerous_paths:
                return ToolOutput(
                    status=ToolStatus.ERROR,
                    error=f"Refusing to delete system path: {abs_path}"
                )
            
            if path.is_file():
                path.unlink()
                deleted_type = "file"
            elif path.is_dir():
                if not recursive and any(path.iterdir()):
                    return ToolOutput(
                        status=ToolStatus.ERROR,
                        error=f"Directory not empty and recursive=false: {file_path}"
                    )
                shutil.rmtree(path)
                deleted_type = "directory"
            else:
                return ToolOutput(
                    status=ToolStatus.ERROR,
                    error=f"Unknown path type: {file_path}"
                )
            
            result = FileDeleteResult(
                deleted_path=str(abs_path),
                type=deleted_type,
                recursive=recursive
            )

            return ToolOutput(
                status=ToolStatus.SUCCESS,
                data=result
            )
            
        except Exception as e:
            return ToolOutput(
                status=ToolStatus.ERROR,
                error=f"Failed to delete: {str(e)}"
            )


# Register the file tools
register_tool(FileReadTool())
register_tool(FileWriteTool())
register_tool(FileListTool())
register_tool(FileDeleteTool())
