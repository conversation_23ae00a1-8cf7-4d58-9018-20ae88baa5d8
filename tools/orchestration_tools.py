"""
Orchestration Tools

Tools for executing multiple tools concurrently and managing complex workflows.

Author: <PERSON>
"""

import asyncio
from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field, validator

from .base import BaseTool, ToolOutput, ToolStatus, register_tool, execute_tool


class ToolExecution(BaseModel):
    """Definition of a single tool execution"""
    tool_name: str = Field(..., description="Full name of the tool to execute (namespace.name)")
    input_data: Dict[str, Any] = Field(..., description="Input data for the tool")
    id: Optional[str] = Field(None, description="Optional identifier for this execution")
    
    class Config:
        json_schema_extra = {
            "examples": [
                {
                    "tool_name": "terminal.execute",
                    "input_data": {"command": "echo 'Hello'", "timeout": 10},
                    "id": "greeting"
                },
                {
                    "tool_name": "file.read",
                    "input_data": {"path": "config.json"}
                }
            ]
        }

    @validator('tool_name')
    def validate_tool_name(cls, v):
        """Validate tool name format"""
        if not v:
            raise ValueError("Tool name cannot be empty")
        if '.' not in v:
            raise ValueError("Tool name must be in format 'namespace.name'")
        return v


class ConcurrentExecuteInput(BaseModel):
    """Input model for concurrent tool execution"""
    tools: List[ToolExecution] = Field(..., min_items=1, description="List of tools to execute concurrently")
    timeout: int = Field(300, ge=1, le=3600, description="Overall timeout in seconds (1-3600)")
    fail_fast: bool = Field(False, description="Stop all executions if any tool fails")
    max_concurrency: int = Field(10, ge=1, le=50, description="Maximum number of concurrent executions (1-50)")
    
    class Config:
        json_schema_extra = {
            "examples": [
                {
                    "tools": [
                        {
                            "tool_name": "terminal.execute",
                            "input_data": {"command": "echo 'Task 1'"},
                            "id": "task1"
                        },
                        {
                            "tool_name": "terminal.execute", 
                            "input_data": {"command": "echo 'Task 2'"},
                            "id": "task2"
                        }
                    ],
                    "timeout": 60,
                    "max_concurrency": 5
                }
            ]
        }

    @validator('tools')
    def validate_tools_not_empty(cls, v):
        """Validate that tools list is not empty"""
        if not v:
            raise ValueError("Tools list cannot be empty")
        return v


class ToolExecutionResult(BaseModel):
    """Result from a single tool execution"""
    tool_name: str = Field(..., description="Name of the executed tool")
    id: Optional[str] = Field(None, description="Identifier for this execution")
    status: str = Field(..., description="Execution status")
    data: Any = Field(None, description="Tool result data")
    error: Optional[str] = Field(None, description="Error message if execution failed")
    execution_time: float = Field(..., ge=0, description="Execution time in seconds")
    
    class Config:
        arbitrary_types_allowed = True


class ConcurrentExecuteResult(BaseModel):
    """Result from concurrent tool execution"""
    total_tools: int = Field(..., ge=0, description="Total number of tools executed")
    successful: int = Field(..., ge=0, description="Number of successful executions")
    failed: int = Field(..., ge=0, description="Number of failed executions")
    total_execution_time: float = Field(..., ge=0, description="Total time for all executions")
    results: List[ToolExecutionResult] = Field(..., description="Individual tool execution results")
    summary: str = Field(..., description="Summary of the execution")
    
    class Config:
        arbitrary_types_allowed = True


class ConcurrentExecuteTool(BaseTool):
    """Tool for executing multiple tools concurrently"""

    def __init__(self):
        super().__init__(namespace="orchestration", name="concurrent")

    def get_description(self) -> str:
        return "Execute multiple tools concurrently to speed up operations"

    def get_input_model(self):
        return ConcurrentExecuteInput
    
    async def execute(self, input_data: Dict[str, Any]) -> ToolOutput:
        start_time = asyncio.get_event_loop().time()

        # Parse input using Pydantic model
        parsed_input = self.parse_input(input_data)

        tools = parsed_input.tools
        timeout = parsed_input.timeout
        fail_fast = parsed_input.fail_fast
        max_concurrency = parsed_input.max_concurrency
        
        try:
            # Create semaphore to limit concurrency
            semaphore = asyncio.Semaphore(max_concurrency)
            
            async def execute_single_tool(tool_exec: ToolExecution) -> ToolExecutionResult:
                """Execute a single tool with semaphore control"""
                async with semaphore:
                    tool_start = asyncio.get_event_loop().time()
                    try:
                        # Execute the tool
                        result = await execute_tool(tool_exec.tool_name, tool_exec.input_data)
                        
                        return ToolExecutionResult(
                            tool_name=tool_exec.tool_name,
                            id=tool_exec.id,
                            status=result.status,
                            data=result.data,
                            error=result.error,
                            execution_time=round(asyncio.get_event_loop().time() - tool_start, 3)
                        )
                    except Exception as e:
                        return ToolExecutionResult(
                            tool_name=tool_exec.tool_name,
                            id=tool_exec.id,
                            status=ToolStatus.ERROR.value,
                            data=None,
                            error=f"Tool execution failed: {str(e)}",
                            execution_time=round(asyncio.get_event_loop().time() - tool_start, 3)
                        )
            
            # Create tasks for all tool executions
            tasks = [asyncio.create_task(execute_single_tool(tool_exec)) for tool_exec in tools]
            
            # Execute all tasks concurrently with timeout
            try:
                if fail_fast:
                    # Use asyncio.wait with FIRST_COMPLETED and check for failures
                    results = []
                    remaining_tasks = set(tasks)
                    failed = False

                    while remaining_tasks and not failed:
                        done, pending = await asyncio.wait(
                            remaining_tasks,
                            timeout=timeout,
                            return_when=asyncio.FIRST_COMPLETED
                        )

                        if not done:  # Timeout occurred
                            break

                        # Process completed tasks
                        for task in done:
                            try:
                                result = task.result()
                                results.append(result)

                                # If this task failed, cancel all remaining tasks
                                if result.status != ToolStatus.SUCCESS.value:
                                    for pending_task in pending:
                                        pending_task.cancel()

                                    # Add cancelled tasks to results
                                    for i, original_task in enumerate(tasks):
                                        if original_task in pending:
                                            results.append(ToolExecutionResult(
                                                tool_name=tools[i].tool_name,
                                                id=tools[i].id,
                                                status=ToolStatus.ERROR.value,
                                                data=None,
                                                error="Cancelled due to fail-fast",
                                                execution_time=0.0
                                            ))
                                    failed = True
                                    break

                            except Exception as e:
                                # Find which tool this task corresponds to
                                task_index = tasks.index(task)
                                results.append(ToolExecutionResult(
                                    tool_name=tools[task_index].tool_name,
                                    id=tools[task_index].id,
                                    status=ToolStatus.ERROR.value,
                                    data=None,
                                    error=f"Task exception: {str(e)}",
                                    execution_time=0.0
                                ))

                                # Cancel remaining tasks on exception
                                for pending_task in pending:
                                    pending_task.cancel()
                                failed = True
                                break

                        if not failed:
                            remaining_tasks = pending
                else:
                    # Use asyncio.gather with return_exceptions=True to collect all results
                    results = await asyncio.wait_for(
                        asyncio.gather(*tasks, return_exceptions=True),
                        timeout=timeout
                    )

                    # Convert exceptions to error results
                    processed_results = []
                    for i, result in enumerate(results):
                        if isinstance(result, Exception):
                            processed_results.append(ToolExecutionResult(
                                tool_name=tools[i].tool_name,
                                id=tools[i].id,
                                status=ToolStatus.ERROR.value,
                                data=None,
                                error=f"Execution exception: {str(result)}",
                                execution_time=0.0
                            ))
                        else:
                            processed_results.append(result)
                    results = processed_results
                
            except asyncio.TimeoutError:
                # Handle timeout - cancel remaining tasks
                for task in tasks:
                    if not task.done():
                        task.cancel()
                
                # Collect results from completed tasks
                results = []
                for i, task in enumerate(tasks):
                    if task.done() and not task.cancelled():
                        try:
                            results.append(task.result())
                        except Exception as e:
                            results.append(ToolExecutionResult(
                                tool_name=tools[i].tool_name,
                                id=tools[i].id,
                                status=ToolStatus.ERROR.value,
                                data=None,
                                error=f"Task exception: {str(e)}",
                                execution_time=0.0
                            ))
                    else:
                        results.append(ToolExecutionResult(
                            tool_name=tools[i].tool_name,
                            id=tools[i].id,
                            status=ToolStatus.TIMEOUT.value,
                            data=None,
                            error=f"Execution timed out after {timeout} seconds",
                            execution_time=timeout
                        ))
            
            # Calculate summary statistics
            total_execution_time = round(asyncio.get_event_loop().time() - start_time, 3)
            successful = len([r for r in results if r.status == ToolStatus.SUCCESS.value])
            failed = len(results) - successful
            
            summary = f"Executed {len(results)} tools in {total_execution_time}s: {successful} successful, {failed} failed"
            
            concurrent_result = ConcurrentExecuteResult(
                total_tools=len(results),
                successful=successful,
                failed=failed,
                total_execution_time=total_execution_time,
                results=results,
                summary=summary
            )
            
            # Determine overall status
            if failed == 0:
                status = ToolStatus.SUCCESS
            elif any(r.status == ToolStatus.TIMEOUT.value for r in results):
                status = ToolStatus.TIMEOUT
            else:
                status = ToolStatus.ERROR
            
            return ToolOutput(
                status=status,
                data=concurrent_result,
                execution_time=total_execution_time,
                metadata={"max_concurrency": max_concurrency, "fail_fast": fail_fast}
            )
                
        except Exception as e:
            execution_time = round(asyncio.get_event_loop().time() - start_time, 3)
            
            return ToolOutput(
                status=ToolStatus.ERROR,
                error=f"Concurrent execution failed: {str(e)}",
                execution_time=execution_time
            )


# Register the orchestration tools
register_tool(ConcurrentExecuteTool())
