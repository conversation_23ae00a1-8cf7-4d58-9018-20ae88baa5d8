# MCP Integration Demo Workflow
# Demonstrates the new MCP server integration with the workflow engine

name: "MCP Integration Demo"
description: "Demonstrates executing tools from MCP servers alongside local tools"
version: "1.0"

# Variables
variables:
  demo_message: "Hello from MCP-enabled workflow!"
  output_dir: "./mcp-demo"

# Workflow settings
settings:
  max_concurrency: 2
  timeout: 120
  fail_fast: false

# Output configuration
output:
  summary_file: "./mcp_demo_summary.json"
  format: "json"

# MCP Server configurations
mcp_servers:
  - name: "orchestra_server"
    script_path: "/Users/<USER>/Developer/projects/python/orchestra/app/server.py"
    auto_connect: true

# Workflow steps
steps:
  # Step 1: Create output directory using local tool
  - name: "setup_directory"
    type: "tool"
    tool_name: "terminal.execute"
    input_data:
      command: "mkdir -p ${output_dir}"
      timeout: 10

  - name:
      type: "tool"
      tool_name: "orchestra_server.test"
      input_data:
        url: "localhost:8080"
      depends_on: ["setup_directory"]

  # Step 2: Use local file tool to create a demo file
  - name: "create_local_file"
    type: "tool"
    tool_name: "file.write"
    input_data:
      path: "${output_dir}/local_demo.txt"
      content: "${demo_message}\nCreated by local file tool"
      overwrite: true
    depends_on: ["setup_directory"]


  # Step 5: List files using local tool
  - name: "list_files"
    type: "tool"
    tool_name: "file.list"
    input_data:
      path: "${output_dir}"
      recursive: false
    depends_on: ["create_local_file", "create_mcp_file"]

  # Step 6: Final summary using local tool
  - name: "final_summary"
    type: "tool"
    tool_name: "terminal.execute"
    input_data:
      command: "echo 'MCP integration demo completed successfully!'"
      timeout: 5
    depends_on: ["list_files", "github_operation"]
