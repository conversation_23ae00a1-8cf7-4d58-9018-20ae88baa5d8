# Repository Cloning Workflow
# Demonstrates the new standalone workflow system

name: "Repository Cloning Workflow"
description: "Clone multiple GitHub repositories using Copier templates"
version: "1.0"

# Workflow variables that can be substituted in steps
variables:
  base_destination: "./cloned-repos"
  author_name: "Workflow Demo User"
  project_prefix: "demo"

# Workflow execution settings
settings:
  max_concurrency: 2
  timeout: 300
  fail_fast: false
  continue_on_error: true

# Output configuration
output:
  summary_file: "./workflow_execution_summary.json"
  format: "json"

# Workflow steps
steps:
  # Step 1: Create base directory
  - name: "create_base_directory"
    type: "tool"
    tool_name: "terminal.execute"
    input_data:
      command: "mkdir -p ${base_destination}"
      timeout: 10
    depends_on: []

  # Step 2: Clone first repository
  - name: "clone_copier_templates"
    type: "tool"
    tool_name: "copier.clone"
    input_data:
      template_url: "https://github.com/Josephmaclean/nuxt-template.git"
      destination: "${base_destination}/${project_prefix}-templates"
      data:
        project_name: "${project_prefix} Templates"
        author_name: "${author_name}"
        description: "Cloned via standalone workflow system"
      overwrite: true
    depends_on: ["create_base_directory"]

  # Step 3: Clone second repository using git (parallel with first)
  - name: "clone_simple_template"
    type: "tool"
    tool_name: "terminal.execute"
    input_data:
      command: "git clone https://github.com/Josephmaclean/aws-nuke.git ${base_destination}/${project_prefix}-simple"
      timeout: 60
    depends_on: ["create_base_directory"]

  # Step 4: List cloned repositories
  - name: "list_cloned_repos"
    type: "tool"
    tool_name: "file.list"
    input_data:
      path: "${base_destination}"
    depends_on: ["clone_copier_templates", "clone_simple_template"]

  # Step 5: Generate summary report
  - name: "generate_summary"
    type: "tool"
    tool_name: "terminal.execute"
    input_data:
      command: "echo 'Workflow completed successfully. Repositories cloned to ${base_destination}'"
      timeout: 5
    depends_on: ["list_cloned_repos"]
