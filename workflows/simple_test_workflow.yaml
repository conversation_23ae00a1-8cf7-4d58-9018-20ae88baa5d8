# Simple Test Workflow
# A basic workflow to test the workflow system functionality

name: "Simple Test Workflow"
description: "Test basic workflow functionality without complex operations"
version: "1.0"

# Workflow variables
variables:
  test_dir: "./test-workflow-output"
  test_message: "Hello from workflow system"

# Workflow execution settings
settings:
  max_concurrency: 2
  timeout: 60
  fail_fast: false

# Output configuration
output:
  summary_file: "./simple_workflow_summary.json"
  format: "json"

# Workflow steps
steps:
  # Step 1: Create test directory
  - name: "create_test_directory"
    type: "tool"
    tool_name: "terminal.execute"
    input_data:
      command: "mkdir -p ${test_dir}"
      timeout: 10
    depends_on: []

  # Step 2: Create a test file
  - name: "create_test_file"
    type: "tool"
    tool_name: "file.write"
    input_data:
      path: "${test_dir}/test.txt"
      content: "${test_message}\nGenerated at: $(date)"
      overwrite: true
    depends_on: ["create_test_directory"]

  # Step 3: List directory contents
  - name: "list_directory"
    type: "tool"
    tool_name: "file.list"
    input_data:
      path: "${test_dir}"
    depends_on: ["create_test_file"]

  # Step 4: Read the test file
  - name: "read_test_file"
    type: "tool"
    tool_name: "file.read"
    input_data:
      path: "${test_dir}/test.txt"
    depends_on: ["create_test_file"]

  # Step 5: Generate summary
  - name: "generate_summary"
    type: "tool"
    tool_name: "terminal.execute"
    input_data:
      command: "echo 'Simple workflow test completed successfully. Files created in ${test_dir}'"
      timeout: 5
    depends_on: ["list_directory", "read_test_file"]
