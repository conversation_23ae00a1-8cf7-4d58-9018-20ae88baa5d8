"""
Output Writer for Workflow Engine

Handles writing workflow results to output files in various formats.

Author: Assistant
"""

import asyncio
import json
import time
from .models import WorkflowResult


class OutputWriter:
    """Handles writing workflow output files"""

    def __init__(self, tools_module):
        self.tools_module = tools_module

    async def write_output_files(self, result: WorkflowResult, output_config) -> None:
        """Write output files if configured"""
        if output_config.summary_file:
            try:
                summary_data = {
                    "workflow_result": result.dict(),
                    "timestamp": time.time()
                }
                
                if output_config.format.lower() == "yaml":
                    content = self._format_as_yaml(summary_data)
                else:
                    content = json.dumps(summary_data, indent=2)
                
                # Use tools to write the file
                await asyncio.get_event_loop().run_in_executor(
                    None, self.tools_module.execute, "file.write", {
                        "path": output_config.summary_file,
                        "content": content,
                        "overwrite": True
                    }
                )
                
            except Exception as e:
                print(f"⚠️  Failed to write summary file: {e}")

    def _format_as_yaml(self, data: dict) -> str:
        """Format data as YAML"""
        try:
            import yaml
            return yaml.dump(data, default_flow_style=False)
        except ImportError:
            # Fallback to JSON if YAML is not available
            print("⚠️  YAML library not available, falling back to JSON format")
            return json.dumps(data, indent=2)
