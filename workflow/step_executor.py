"""
Step Executor for Workflow Engine

Handles execution of individual workflow steps including tool execution,
parallel execution, and variable substitution.

Author: Assistant
"""

import asyncio
import time
from typing import Any, Dict
from .models import WorkflowStep, WorkflowConfig, StepResult, StepType
from .mcp_adapter import MCPAdapter


class StepExecutor:
    """Handles execution of individual workflow steps"""

    def __init__(self, tools_module, mcp_adapter: MCPAdapter):
        self.tools_module = tools_module
        self.mcp_adapter = mcp_adapter

    async def execute_step(self, step: WorkflowStep, config: WorkflowConfig) -> StepResult:
        """Execute a single workflow step"""
        
        start_time = time.time()
        
        try:
            if step.type == StepType.TOOL:
                return await self._execute_tool_step(step, config, start_time)
            
            elif step.type == StepType.PARALLEL:
                return await self._execute_parallel_step(step, config, start_time)
            
            else:
                # Other step types not yet implemented
                return StepResult(
                    step_name=step.name,
                    step_type=step.type,
                    status="error",
                    execution_time=round(time.time() - start_time, 3),
                    error=f"Step type '{step.type}' not yet implemented",
                    tool_name=step.tool_name
                )
        
        except Exception as e:
            return StepResult(
                step_name=step.name,
                step_type=step.type,
                status="error",
                execution_time=round(time.time() - start_time, 3),
                error=str(e),
                tool_name=step.tool_name
            )

    async def _execute_tool_step(self, step: WorkflowStep, config: WorkflowConfig, start_time: float) -> StepResult:
        """Execute a tool step"""
        if not step.tool_name:
            raise ValueError(f"Tool step '{step.name}' missing tool_name")
        
        # Substitute variables in input data
        input_data = self.substitute_variables(step.input_data, config.variables)

        # Check if this is an MCP tool or local tool
        if self.mcp_adapter.is_mcp_tool(step.tool_name):
            # Execute MCP tool
            result = await self.mcp_adapter.execute_tool(step.tool_name, input_data)
        else:
            # Execute local tool (tools.execute is async-compatible)
            result = await asyncio.get_event_loop().run_in_executor(
                None, self.tools_module.execute, step.tool_name, input_data
            )
        
        return StepResult(
            step_name=step.name,
            step_type=step.type,
            status=result.status,
            execution_time=round(time.time() - start_time, 3),
            data=result.data,
            error=result.error,
            tool_name=step.tool_name
        )

    async def _execute_parallel_step(self, step: WorkflowStep, config: WorkflowConfig, start_time: float) -> StepResult:
        """Execute a parallel step"""
        if not step.parallel_steps:
            raise ValueError(f"Parallel step '{step.name}' has no parallel_steps defined")
        
        # Execute all parallel steps concurrently
        tasks = [self.execute_step(parallel_step, config) for parallel_step in step.parallel_steps]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Aggregate results
        successful = len([r for r in results if isinstance(r, StepResult) and r.status == "success"])
        total = len(results)
        
        return StepResult(
            step_name=step.name,
            step_type=step.type,
            status="success" if successful == total else "partial",
            execution_time=round(time.time() - start_time, 3),
            data={"parallel_results": results, "successful": successful, "total": total},
            tool_name=None
        )

    def substitute_variables(self, data: Any, variables: Dict[str, Any]) -> Any:
        """Substitute variables in data structures"""
        if isinstance(data, str):
            # Simple variable substitution: ${variable_name}
            for var_name, var_value in variables.items():
                data = data.replace(f"${{{var_name}}}", str(var_value))
            return data
        elif isinstance(data, dict):
            return {k: self.substitute_variables(v, variables) for k, v in data.items()}
        elif isinstance(data, list):
            return [self.substitute_variables(item, variables) for item in data]
        else:
            return data
