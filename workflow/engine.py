"""
Workflow Execution Engine

The main engine for executing workflows. Handles step orchestration,
dependency resolution, parallel execution, and error handling.

Author: Assistant
"""

import asyncio
import time
from typing import Dict, Any, List, Optional, Set, Union
from .models import (
    WorkflowConfig, WorkflowStep, WorkflowResult, StepResult, StepType,
    WorkflowEvent, EventCallback, AsyncEventCallback, MCPServerConfig
)
from .readers import get_reader
from .mcp_adapter import MCPAdapter, MCPServerConfig as AdapterMCPServerConfig
from .dependency_graph import DependencyGraph
from .event_system import EventManager, WorkflowEventFactory
from .step_executor import StepExecutor
from .output_writer import OutputWriter
from .dry_run_handler import DryRunHandler


class WorkflowEngine:
    """Main workflow execution engine with MCP support"""

    def __init__(self, event_callbacks: Optional[List[Union[EventCallback, AsyncEventCallback]]] = None):
        self.tools_module = None
        self._load_tools_module()

        # Component initialization
        self.event_manager = EventManager(event_callbacks)
        self.mcp_adapter = MCPAdapter()
        self.step_executor = StepExecutor(self.tools_module, self.mcp_adapter)
        self.output_writer = OutputWriter(self.tools_module)
        self.dry_run_handler = DryRunHandler()
    
    def _load_tools_module(self):
        """Load the tools module for executing tool steps"""
        try:
            import tools
            self.tools_module = tools
        except ImportError:
            raise ImportError("Tools module not available. Ensure tools package is installed.")

    async def _setup_mcp_servers(self, mcp_configs: List[MCPServerConfig]) -> None:
        """Setup and connect to MCP servers"""
        for config in mcp_configs:
            if config.auto_connect:
                try:
                    adapter_config = AdapterMCPServerConfig(
                        name=config.name,
                        script_path=config.script_path,
                        env=config.env
                    )
                    await self.mcp_adapter.add_server(adapter_config)
                    print(f"✅ Connected to MCP server: {config.name}")
                except Exception as e:
                    print(f"⚠️  Failed to connect to MCP server {config.name}: {str(e)}")

    async def _cleanup_mcp_servers(self) -> None:
        """Cleanup MCP server connections"""
        await self.mcp_adapter.cleanup()
    
    async def execute_from_file(self, config_file: str, dry_run: bool = False, 
                               variables: Optional[Dict[str, Any]] = None) -> WorkflowResult:
        """Execute workflow from configuration file"""
        
        # Read and parse configuration
        reader = get_reader(config_file)
        config = reader.read(config_file)
        
        # Override variables if provided
        if variables:
            config.variables.update(variables)
        
        return await self.execute(config, dry_run=dry_run, config_file=config_file)
    
    async def execute(self, config: WorkflowConfig, dry_run: bool = False,
                     config_file: Optional[str] = None) -> WorkflowResult:
        """Execute a workflow configuration"""

        start_time = time.time()

        # Fire workflow started event
        await self.event_manager.fire_event(
            WorkflowEventFactory.workflow_started(config, config_file, dry_run)
        )

        if dry_run:
            return await self.dry_run_handler.execute_dry_run(config, config_file, start_time)

        # Setup MCP servers if configured
        if config.mcp_servers:
            try:
                await self._setup_mcp_servers(config.mcp_servers)
            except Exception as e:
                print(f"⚠️  MCP server setup failed: {str(e)}")

        # Build dependency graph with validation and cycle detection
        try:
            dependency_graph = DependencyGraph(config.steps)
        except ValueError as e:
            # Return error result for dependency issues
            return WorkflowResult(
                workflow_name=config.name,
                config_file=config_file,
                total_steps=len(config.steps),
                executed_steps=0,
                successful_steps=0,
                failed_steps=len(config.steps),
                skipped_steps=0,
                total_execution_time=round(time.time() - start_time, 3),
                status="failed",
                steps=[StepResult(
                    step_name="dependency_validation",
                    step_type=StepType.TOOL,
                    status="error",
                    execution_time=0.0,
                    error=f"Dependency validation failed: {str(e)}"
                )],
                summary=f"Workflow failed due to dependency issues: {str(e)}",
                dry_run=False
            )

        # Execute steps using dependency graph
        step_results = []
        executed_steps = set()
        failed_steps = set()
        steps_by_name = {step.name: step for step in config.steps}

        try:
            # Execute steps respecting dependencies using graph
            while len(executed_steps) + len(failed_steps) < len(config.steps):
                # Get steps that are ready to execute
                ready_step_names = dependency_graph.get_ready_steps(executed_steps, failed_steps)

                if not ready_step_names:
                    # No more steps can execute - mark remaining as skipped
                    remaining_step_names = [
                        name for name in steps_by_name.keys()
                        if name not in executed_steps and name not in failed_steps
                    ]

                    for step_name in remaining_step_names:
                        step = steps_by_name[step_name]
                        step_results.append(StepResult(
                            step_name=step.name,
                            step_type=step.type,
                            status="skipped",
                            execution_time=0.0,
                            error="Skipped due to failed dependencies",
                            tool_name=step.tool_name
                        ))
                        failed_steps.add(step.name)
                    break

                ready_steps = [steps_by_name[name] for name in ready_step_names]
                
                # Execute ready steps (potentially in parallel)
                if len(ready_steps) == 1 or config.settings.max_concurrency == 1:
                    # Execute sequentially
                    for step in ready_steps:
                        result = await self.step_executor.execute_step(step, config)
                        step_results.append(result)

                        # Fire step completed event
                        await self.event_manager.fire_event(
                            WorkflowEventFactory.step_completed(
                                config, step.name, result, executed_steps, failed_steps, step_results
                            )
                        )

                        if result.status == "success":
                            executed_steps.add(step.name)
                        else:
                            failed_steps.add(step.name)
                            if config.settings.fail_fast:
                                break
                else:
                    # Execute in parallel with concurrency limit
                    semaphore = asyncio.Semaphore(min(config.settings.max_concurrency, len(ready_steps)))

                    async def execute_with_semaphore(step):
                        async with semaphore:
                            return await self.step_executor.execute_step(step, config)

                    tasks = [execute_with_semaphore(step) for step in ready_steps]
                    results = await asyncio.gather(*tasks, return_exceptions=True)

                    for i, result in enumerate(results):
                        if isinstance(result, Exception):
                            result = StepResult(
                                step_name=ready_steps[i].name,
                                step_type=ready_steps[i].type,
                                status="error",
                                execution_time=0.0,
                                error=f"Execution exception: {str(result)}",
                                tool_name=ready_steps[i].tool_name
                            )

                        step_results.append(result)

                        # Fire step completed event for parallel execution
                        await self.event_manager.fire_event(
                            WorkflowEventFactory.step_completed(
                                config, result.step_name, result, executed_steps, failed_steps,
                                step_results, parallel_execution=True
                            )
                        )

                        if result.status == "success":
                            executed_steps.add(result.step_name)
                        else:
                            failed_steps.add(result.step_name)
                
                # Check fail_fast condition
                if config.settings.fail_fast and failed_steps:
                    break
        
        except Exception as e:
            # Handle unexpected errors
            step_results.append(StepResult(
                step_name="workflow_error",
                step_type=StepType.TOOL,
                status="error",
                execution_time=0.0,
                error=f"Workflow execution error: {str(e)}"
            ))
        
        # Calculate final statistics
        total_time = time.time() - start_time
        successful_count = len([r for r in step_results if r.status == "success"])
        failed_count = len([r for r in step_results if r.status == "error"])
        skipped_count = len([r for r in step_results if r.status == "skipped"])
        
        # Determine overall status
        if failed_count == 0:
            overall_status = "success"
        elif successful_count == 0:
            overall_status = "failed"
        else:
            overall_status = "partial"
        
        summary = f"Executed {len(step_results)} steps: {successful_count} successful, {failed_count} failed, {skipped_count} skipped"
        
        result = WorkflowResult(
            workflow_name=config.name,
            config_file=config_file,
            total_steps=len(config.steps),
            executed_steps=len(executed_steps),
            successful_steps=successful_count,
            failed_steps=failed_count,
            skipped_steps=skipped_count,
            total_execution_time=round(total_time, 3),
            status=overall_status,
            steps=step_results,
            summary=summary,
            dry_run=False
        )
        
        # Write output files if configured
        await self.output_writer.write_output_files(result, config.output)

        # Fire workflow completed event
        await self.event_manager.fire_event(
            WorkflowEventFactory.workflow_completed(
                config, executed_steps, failed_steps, step_results,
                result.total_execution_time, result.status, result.summary, config_file
            )
        )

        # Cleanup MCP servers
        await self._cleanup_mcp_servers()

        return result