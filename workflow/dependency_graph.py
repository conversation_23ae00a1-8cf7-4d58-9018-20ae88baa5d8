"""
Dependency Graph for Workflow Steps

Handles dependency resolution, cycle detection, and execution level calculation
for workflow steps using graph algorithms.

Author: Assistant
"""

from typing import Dict, List, Set, Optional
from collections import deque
from .models import WorkflowStep


class DependencyGraph:
    """
    Dependency graph for workflow steps with topological sorting and cycle detection.

    This class provides efficient dependency resolution using graph algorithms
    instead of the previous O(n²) manual approach.
    """

    def __init__(self, steps: List[WorkflowStep]):
        self.steps_by_name = {step.name: step for step in steps}
        self.graph: Dict[str, Set[str]] = {}  # step -> dependencies
        self.reverse_graph: Dict[str, Set[str]] = {}  # step -> dependents
        self.execution_levels: List[List[str]] = []
        self._build_graphs()
        self._validate_dependencies()
        self._detect_cycles()
        self._calculate_execution_levels()

    def _build_graphs(self) -> None:
        """Build forward and reverse dependency graphs"""
        # Initialize graphs
        for step_name in self.steps_by_name:
            self.graph[step_name] = set()
            self.reverse_graph[step_name] = set()

        # Build dependency relationships
        for step in self.steps_by_name.values():
            self.graph[step.name] = set(step.depends_on)
            for dep in step.depends_on:
                if dep in self.reverse_graph:
                    self.reverse_graph[dep].add(step.name)

    def _validate_dependencies(self) -> None:
        """Validate that all dependencies exist"""
        for step in self.steps_by_name.values():
            for dep in step.depends_on:
                if dep not in self.steps_by_name:
                    raise ValueError(f"Step '{step.name}' depends on non-existent step '{dep}'")

    def _detect_cycles(self) -> None:
        """Detect circular dependencies using DFS"""
        WHITE, GRAY, BLACK = 0, 1, 2
        colors = {step: WHITE for step in self.steps_by_name}

        def dfs(node: str, path: List[str]) -> Optional[List[str]]:
            if colors[node] == GRAY:
                # Found a cycle - return the cycle path
                cycle_start = path.index(node)
                return path[cycle_start:] + [node]

            if colors[node] == BLACK:
                return None

            colors[node] = GRAY
            path.append(node)

            for dependency in self.graph[node]:
                cycle = dfs(dependency, path)
                if cycle:
                    return cycle

            path.pop()
            colors[node] = BLACK
            return None

        for step in self.steps_by_name:
            if colors[step] == WHITE:
                cycle = dfs(step, [])
                if cycle:
                    cycle_str = " -> ".join(cycle)
                    raise ValueError(f"Circular dependency detected: {cycle_str}")

    def _calculate_execution_levels(self) -> None:
        """Calculate execution levels using topological sort (Kahn's algorithm)"""
        # Calculate in-degrees
        in_degree = {step: len(deps) for step, deps in self.graph.items()}

        # Initialize queue with steps that have no dependencies
        queue = deque([step for step, degree in in_degree.items() if degree == 0])

        levels = []

        while queue:
            # All steps in current queue can execute in parallel
            current_level = list(queue)
            levels.append(current_level)

            # Process current level
            next_queue = deque()
            for step in current_level:
                # Remove this step and update in-degrees of dependents
                for dependent in self.reverse_graph[step]:
                    in_degree[dependent] -= 1
                    if in_degree[dependent] == 0:
                        next_queue.append(dependent)

            queue = next_queue

        # Verify all steps were processed (no cycles)
        total_processed = sum(len(level) for level in levels)
        if total_processed != len(self.steps_by_name):
            remaining = [step for step, degree in in_degree.items() if degree > 0]
            raise ValueError(f"Failed to resolve dependencies for steps: {remaining}")

        self.execution_levels = levels

    def get_execution_levels(self) -> List[List[str]]:
        """Get the calculated execution levels"""
        return self.execution_levels

    def get_ready_steps(self, executed_steps: Set[str], failed_steps: Set[str]) -> List[str]:
        """Get steps that are ready to execute given current state"""
        ready = []
        for step_name in self.steps_by_name:
            if (step_name not in executed_steps and
                step_name not in failed_steps and
                all(dep in executed_steps for dep in self.graph[step_name])):
                ready.append(step_name)
        return ready

    def get_step_dependencies(self, step_name: str) -> Set[str]:
        """Get direct dependencies of a step"""
        return self.graph.get(step_name, set())

    def get_step_dependents(self, step_name: str) -> Set[str]:
        """Get direct dependents of a step"""
        return self.reverse_graph.get(step_name, set())

    def get_affected_steps(self, failed_step: str) -> Set[str]:
        """Get all steps that will be affected by a failed step"""
        affected = set()
        queue = deque([failed_step])

        while queue:
            current = queue.popleft()
            for dependent in self.reverse_graph.get(current, set()):
                if dependent not in affected:
                    affected.add(dependent)
                    queue.append(dependent)

        return affected
