"""
MCP (Model Context Protocol) Adapter for Workflow Engine

This module provides integration between MCP servers and the workflow engine,
allowing workflows to execute tools from MCP servers alongside local tools.

Author: Assistant
"""

import asyncio
import json
from typing import Dict, Any, List, Optional, Set
from contextlib import AsyncExitStack
from pathlib import Path

from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

from tools.base import ToolOutput, ToolStatus


class MCPServerConfig:
    """Configuration for an MCP server"""
    
    def __init__(self, name: str, script_path: str, env: Optional[Dict[str, str]] = None):
        self.name = name
        self.script_path = script_path
        self.env = env or {}
        self.is_python = script_path.endswith('.py')
        self.is_js = script_path.endswith('.js')
        
        if not (self.is_python or self.is_js):
            raise ValueError(f"Server script must be a .py or .js file: {script_path}")


class MCPServerConnection:
    """Manages connection to a single MCP server"""
    
    def __init__(self, config: MCPServerConfig):
        self.config = config
        self.session: Optional[ClientSession] = None
        self.exit_stack = AsyncExitStack()
        self.tools: Dict[str, Any] = {}
        self.connected = False
    
    async def connect(self) -> None:
        """Connect to the MCP server"""
        if self.connected:
            return
        
        try:
            command = "python" if self.config.is_python else "node"
            server_params = StdioServerParameters(
                command=command,
                args=[self.config.script_path],
                env=self.config.env
            )
            
            stdio_transport = await self.exit_stack.enter_async_context(
                stdio_client(server_params)
            )
            stdio, write = stdio_transport
            self.session = await self.exit_stack.enter_async_context(
                ClientSession(stdio, write)
            )
            
            await self.session.initialize()
            
            # Load available tools
            response = await self.session.list_tools()
            self.tools = {tool.name: tool for tool in response.tools}
            self.connected = True
            
        except Exception as e:
            raise ConnectionError(f"Failed to connect to MCP server {self.config.name}: {str(e)}")
    
    async def disconnect(self) -> None:
        """Disconnect from the MCP server"""
        if self.connected:
            await self.exit_stack.aclose()
            self.connected = False
            self.session = None
            self.tools = {}
    
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> ToolOutput:
        """Call a tool on this MCP server"""
        if not self.connected or not self.session:
            return ToolOutput(
                status=ToolStatus.ERROR,
                error=f"Not connected to MCP server {self.config.name}"
            )
        
        if tool_name not in self.tools:
            return ToolOutput(
                status=ToolStatus.ERROR,
                error=f"Tool '{tool_name}' not found on MCP server {self.config.name}"
            )
        
        try:
            start_time = asyncio.get_event_loop().time()
            result = await self.session.call_tool(tool_name, arguments)
            execution_time = round(asyncio.get_event_loop().time() - start_time, 3)
            
            # Convert MCP result to ToolOutput
            if result.isError:
                return ToolOutput(
                    status=ToolStatus.ERROR,
                    error=f"MCP tool error: {result.content}",
                    execution_time=execution_time
                )
            else:
                return ToolOutput(
                    status=ToolStatus.SUCCESS,
                    data=result.content,
                    execution_time=execution_time,
                    metadata={"mcp_server": self.config.name, "tool_name": tool_name}
                )
                
        except Exception as e:
            return ToolOutput(
                status=ToolStatus.ERROR,
                error=f"Failed to call MCP tool '{tool_name}': {str(e)}",
                execution_time=0.0
            )
    
    def get_tool_names(self) -> List[str]:
        """Get list of available tool names"""
        return list(self.tools.keys())
    
    def get_tool_info(self, tool_name: str) -> Optional[Dict[str, Any]]:
        """Get information about a specific tool"""
        tool = self.tools.get(tool_name)
        if tool:
            return {
                "name": tool.name,
                "description": tool.description,
                "input_schema": tool.inputSchema,
                "server": self.config.name
            }
        return None


class MCPAdapter:
    """
    Adapter that manages multiple MCP server connections and provides
    a unified interface for executing MCP tools in workflows.
    """
    
    def __init__(self):
        self.servers: Dict[str, MCPServerConnection] = {}
        self.tool_to_server: Dict[str, str] = {}  # tool_name -> server_name
    
    async def add_server(self, config: MCPServerConfig) -> None:
        """Add and connect to an MCP server"""
        if config.name in self.servers:
            raise ValueError(f"MCP server '{config.name}' already exists")
        
        connection = MCPServerConnection(config)
        await connection.connect()
        
        self.servers[config.name] = connection
        
        # Update tool routing
        for tool_name in connection.get_tool_names():
            if tool_name in self.tool_to_server:
                existing_server = self.tool_to_server[tool_name]
                print(f"Warning: Tool '{tool_name}' exists on both '{existing_server}' and '{config.name}' servers")
            self.tool_to_server[tool_name] = config.name
    
    async def remove_server(self, server_name: str) -> None:
        """Remove and disconnect from an MCP server"""
        if server_name not in self.servers:
            return
        
        connection = self.servers[server_name]
        
        # Remove tool routing for this server
        tools_to_remove = [tool for tool, server in self.tool_to_server.items() if server == server_name]
        for tool in tools_to_remove:
            del self.tool_to_server[tool]
        
        await connection.disconnect()
        del self.servers[server_name]
    
    async def execute_tool(self, tool_name: str, arguments: Dict[str, Any]) -> ToolOutput:
        """Execute a tool on the appropriate MCP server"""
        server_name = self.tool_to_server.get(tool_name)
        if not server_name:
            return ToolOutput(
                status=ToolStatus.ERROR,
                error=f"MCP tool '{tool_name}' not found on any connected server"
            )
        
        connection = self.servers.get(server_name)
        if not connection:
            return ToolOutput(
                status=ToolStatus.ERROR,
                error=f"MCP server '{server_name}' not connected"
            )
        
        return await connection.call_tool(tool_name, arguments)
    
    def is_mcp_tool(self, tool_name: str) -> bool:
        """Check if a tool is available on MCP servers"""
        return tool_name in self.tool_to_server
    
    def get_all_mcp_tools(self) -> Dict[str, Dict[str, Any]]:
        """Get information about all available MCP tools"""
        all_tools = {}
        for server_name, connection in self.servers.items():
            for tool_name in connection.get_tool_names():
                tool_info = connection.get_tool_info(tool_name)
                if tool_info:
                    all_tools[tool_name] = tool_info
        return all_tools
    
    def get_server_status(self) -> Dict[str, Dict[str, Any]]:
        """Get status of all MCP servers"""
        status = {}
        for server_name, connection in self.servers.items():
            status[server_name] = {
                "connected": connection.connected,
                "script_path": connection.config.script_path,
                "tool_count": len(connection.tools),
                "tools": list(connection.get_tool_names())
            }
        return status
    
    async def cleanup(self) -> None:
        """Disconnect from all MCP servers"""
        for connection in self.servers.values():
            await connection.disconnect()
        self.servers.clear()
        self.tool_to_server.clear()
